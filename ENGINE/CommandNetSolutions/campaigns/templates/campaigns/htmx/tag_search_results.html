{% if error %}
    <div class="text-center text-danger p-4">
        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
        <p>{{ error }}</p>
        <small class="text-muted">Please try again or contact support if the problem persists.</small>
    </div>
{% elif tags %}
    {% for tag in tags %}
    <div class="tag-option d-flex justify-content-between align-items-center p-2 border-bottom"
         data-tag-id="{{ tag.id }}"
         data-tag-name="{{ tag.name }}">
        <div class="tag-info">
            <span class="badge bg-primary me-2">{{ tag.name }}</span>
            {% if tag.category %}
                <span class="badge" style="background-color: {{ tag.category.color }}">
                    {{ tag.category.name }}
                </span>
            {% else %}
                <span class="badge bg-secondary">No Category</span>
            {% endif %}
            {% if tag.description %}
                <div class="text-muted small mt-1">{{ tag.description|truncatechars:100 }}</div>
            {% endif %}
        </div>
        <button type="button"
                class="btn btn-sm btn-outline-primary select-tag-btn"
                data-tag-id="{{ tag.id }}"
                data-tag-name="{{ tag.name }}">
            <i class="fas fa-plus"></i> Select
        </button>
    </div>
    {% endfor %}
{% else %}
    <div class="text-center text-muted p-4">
        <i class="fas fa-search fa-2x mb-2"></i>
        <p>No tags found matching your criteria.</p>
    </div>
{% endif %}
