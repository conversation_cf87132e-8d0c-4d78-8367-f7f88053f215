{% extends 'campaigns/base.html' %}
{% load static %}

{% block title %}{{ campaign.name }} - White List{% endblock %}

{% block campaign_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="page-title">{{ campaign.name }} - White List</h1>
        <p class="page-subtitle">Manage white listed accounts from this campaign</p>
    </div>
    <div>
        <a href="{% url 'campaigns:campaign_detail' campaign.id %}" class="btn btn-outline-primary btn-icon back-button">
            <i class="fas fa-arrow-left me-1"></i> <span>Back</span>
        </a>
    </div>
</div>

    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-primary">
                    <h5 class="mb-0 text-white">Account Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="stat-card">
                                <h2 class="stat-value">{{ total_accounts }}</h2>
                                <p class="stat-label">Total Accounts</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="stat-card">
                                <h2 class="stat-value">{{ whitelisted_accounts }}</h2>
                                <p class="stat-label">White Listed</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-success">
                    <h5 class="mb-0 text-white">Conversion Rate</h5>
                </div>
                <div class="card-body">
                    <div class="conversion-rate-display">
                        <h1 class="display-4 text-center">{{ conversion_rate|floatformat:1 }}%</h1>
                        <p class="text-center text-muted">Accounts that met whitelist criteria</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <h5>White Listed Accounts</h5>
        </div>
        <div class="btn-action-group pe-3">
            <a href="{% url 'campaigns:campaign_tags' campaign.id %}" class="btn btn-primary me-2 stage3-action-btn">
                <i class="fas fa-tags me-2"></i> Manage Tags
            </a>
            <a href="{% url 'campaigns:campaign_accounts_analyzed' campaign.id %}" class="btn btn-primary me-2 stage3-action-btn">
                <i class="fas fa-users me-2"></i> View All Accounts
            </a>
            <a href="{% url 'campaigns:campaign_accounts_list' campaign.id %}" class="btn btn-outline-secondary stage3-action-btn">
                <i class="fas fa-list me-2"></i> View Discovery
            </a>
        </div>
        <div class="card-body">
            {% if whitelist_entries %}
            <!-- Quick search for client-side filtering -->
            <div class="mb-3">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" id="quick-search" class="form-control" placeholder="Quick search in whitelist...">
                </div>
                <div class="form-text">Search for usernames, tags, or other information in the whitelist.</div>
            </div>

            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th class="sortable" data-field="username">Username</th>
                            <th class="sortable" data-field="full_name">Full Name</th>
                            <th class="sortable" data-field="followers">Followers</th>
                            <th class="sortable" data-field="following">Following</th>
                            <th class="sortable" data-field="number_of_posts">Posts</th>
                            <th>Tags</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for entry in whitelist_entries %}
                        <tr>
                            <td>
                                <a href="https://www.instagram.com/{{ entry.account.username }}/" target="_blank" class="text-decoration-none">
                                    {{ entry.account.username }}
                                    {% if entry.account.is_verified %}
                                    <i class="fas fa-check-circle text-primary ms-1" title="Verified"></i>
                                    {% endif %}
                                </a>
                            </td>
                            <td>{{ entry.account.full_name|default:"-" }}</td>
                            <td>{{ entry.account.followers|default:"0" }}</td>
                            <td>{{ entry.account.following|default:"0" }}</td>
                            <td>{{ entry.account.number_of_posts|default:"0" }}</td>
                            <td>
                                {% if entry.dynamic_tags %}
                                    {% for tag in entry.dynamic_tags %}
                                        <span class="badge bg-secondary me-1" title="{{ tag.description|default:'' }}">
                                            {% if tag.category__name %}
                                                <small class="text-white-50">{{ tag.category__name }}:</small>
                                            {% endif %}
                                            {{ tag.name }}
                                        </span>
                                    {% endfor %}
                                {% else %}
                                    {% if entry.tags %}
                                        {% for tag in entry.tags %}
                                            <span class="badge bg-secondary me-1">{{ tag }}</span>
                                        {% endfor %}
                                    {% else %}
                                        -
                                    {% endif %}
                                {% endif %}
                            </td>

                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            {% include 'includes/pagination.html' with page_obj=page_obj %}
            {% else %}
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="fas fa-star"></i>
                </div>
                <h4>No White Listed Accounts</h4>
                <p>No accounts from this campaign have been added to the white list yet. Accounts are automatically analyzed and whitelisted when they match your campaign's tag criteria.</p>
                <a href="{% url 'campaigns:campaign_tags' campaign.id %}" class="btn btn-primary">
                    <i class="fas fa-tags"></i> Manage Tags
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Add quick search functionality
        $('#quick-search').on('keyup', function() {
            const searchTerm = $(this).val().toLowerCase();
            $('.table tbody tr').each(function() {
                const rowText = $(this).text().toLowerCase();
                $(this).toggle(rowText.indexOf(searchTerm) > -1);
            });
        });

        // Add sorting functionality to table headers
        $('.sortable').each(function() {
            const field = $(this).data('field');
            $(this).append('<span class="sort-icon ms-1"><i class="fas fa-sort"></i></span>');

            $(this).on('click', function(e) {
                e.preventDefault();

                // Get current URL parameters
                const urlParams = new URLSearchParams(window.location.search);
                let sortDirection = '';

                // Check current sort direction
                if (urlParams.get('sort_by') === field) {
                    sortDirection = '-' + field;
                } else if (urlParams.get('sort_by') === '-' + field) {
                    sortDirection = field;
                } else {
                    sortDirection = field;
                }

                // Create new URL with sort parameter
                urlParams.set('sort_by', sortDirection);
                window.location.href = window.location.pathname + '?' + urlParams.toString();
            });
        });

        // Highlight active sort column
        const urlParams = new URLSearchParams(window.location.search);
        const currentSort = urlParams.get('sort_by');
        if (currentSort) {
            const fieldMap = {
                'username': 'Username',
                '-username': 'Username',
                'full_name': 'Full Name',
                '-full_name': 'Full Name',
                'followers': 'Followers',
                '-followers': 'Followers',
                'following': 'Following',
                '-following': 'Following',
                'number_of_posts': 'Posts',
                '-number_of_posts': 'Posts'
            };

            const headerText = fieldMap[currentSort];
            if (headerText) {
                $('.sortable').each(function() {
                    if ($(this).text().trim().replace(/[\n\r]+|[\s]{2,}/g, ' ') === headerText) {
                        $(this).addClass('active-sort');

                        // Update sort icon
                        const icon = currentSort.startsWith('-') ?
                            '<i class="fas fa-sort-down"></i>' :
                            '<i class="fas fa-sort-up"></i>';
                        $(this).find('.sort-icon').html(icon);
                    }
                });
            }
        }
    });
</script>
<style>
    .stat-card {
        text-align: center;
        padding: 15px;
    }
    .stat-value {
        font-size: 2.5rem;
        font-weight: 600;
        margin-bottom: 5px;
    }
    .stat-label {
        color: #6c757d;
        margin-bottom: 0;
    }
    .conversion-rate-display {
        padding: 15px;
    }
    .sortable {
        cursor: pointer;
    }
    .sortable:hover {
        background-color: #f8f9fa;
    }
    .active-sort {
        background-color: #e9ecef;
    }
    .sort-icon {
        display: inline-block;
        width: 16px;
    }

    /* Stage 3 Button Group Width Fix */
    .stage3-action-btn {
        min-width: 160px;
        white-space: nowrap;
    }

    /* Responsive adjustments for smaller screens */
    @media (max-width: 768px) {
        .stage3-action-btn {
            min-width: 140px;
            font-size: 0.875rem;
        }
    }

    @media (max-width: 576px) {
        .btn-action-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .stage3-action-btn {
            min-width: 100%;
            margin-right: 0 !important;
        }
    }
</style>
{% endblock extra_js %}
